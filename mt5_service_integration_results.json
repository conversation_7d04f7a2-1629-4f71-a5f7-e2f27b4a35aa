{"test_suite": "MT5_Service_Integration_Tests", "start_time": "2025-07-09T07:37:46.270293", "tests": {"test_service_initialization": {"test_name": "service_initialization", "timestamp": "2025-07-09T07:37:46.270355", "success": true, "data": {"supported_instruments": 16, "sample_instruments": [{"symbol": "XAUUSD", "name": "Gold/USD", "type": "commodity", "yahoo_symbol": "GC=F"}, {"symbol": "EURUSD", "name": "Euro/USD", "type": "forex", "yahoo_symbol": "EURUSD=X"}, {"symbol": "GBPUSD", "name": "British Pound/USD", "type": "forex", "yahoo_symbol": "GBPUSD=X"}, {"symbol": "USDJPY", "name": "USD/Japanese Yen", "type": "forex", "yahoo_symbol": "USDJPY=X"}, {"symbol": "USDCHF", "name": "USD/Swiss Franc", "type": "forex", "yahoo_symbol": "USDCHF=X"}], "symbol_normalization_test": "XAUUSD", "yahoo_mapping_test": "GC=F"}}, "test_real_time_price_retrieval": {"test_name": "real_time_price_retrieval", "timestamp": "2025-07-09T07:37:46.270480", "success": true, "data": {"symbol": "XAUUSD", "current_price": 3300.7, "currency": "USD", "change": -16.200000000000273, "change_percent": -0.4884078507039788, "market_state": null, "source": "Yahoo Finance"}}, "test_multiple_prices": {"test_name": "multiple_prices", "timestamp": "2025-07-09T07:37:46.525728", "success": true, "data": {"total_symbols": 4, "successful_fetches": 4, "success_rate": "100.0%", "price_summary": {"XAUUSD": {"price": 3300.7, "change_percent": -0.4884078507039788}, "EURUSD": {"price": 1.1719, "change_percent": -0.08525876033763423}, "GBPUSD": {"price": 1.3597, "change_percent": 0.044146861893895516}, "USDJPY": {"price": 146.786, "change_percent": 0.16172065316037282}}}}, "test_historical_data": {"test_name": "historical_data", "timestamp": "2025-07-09T07:37:46.679263", "success": true, "data": {"symbol": "XAUUSD", "interval": "1h", "period": "5d", "total_data_points": 70, "sample_data": [{"timestamp": "2025-07-09T04:00:00", "open": 3301.60009765625, "high": 3304.699951171875, "low": 3299.300048828125, "close": 3302.10009765625, "volume": 1892}, {"timestamp": "2025-07-09T05:00:00", "open": 3302.300048828125, "high": 3306.199951171875, "low": 3297.300048828125, "close": 3297.699951171875, "volume": 4359}, {"timestamp": "2025-07-09T06:00:00", "open": 3297.800048828125, "high": 3301.300048828125, "low": 3293.5, "close": 3300.699951171875, "volume": 5655}, {"timestamp": "2025-07-09T07:00:00", "open": 3300.60009765625, "high": 3302.800048828125, "low": 3298.199951171875, "close": 3300.60009765625, "volume": 2321}, {"timestamp": "2025-07-09T07:27:45", "open": 3300.699951171875, "high": 3300.699951171875, "low": 3300.699951171875, "close": 3300.699951171875, "volume": 0}], "source": "Yahoo Finance"}}, "test_error_handling": {"test_name": "error_handling", "timestamp": "2025-07-09T07:37:46.768660", "success": true, "data": {"invalid_symbol_response": {"success": false, "error": "Unsupported symbol: INVALID_SYMBOL", "symbol": "INVALIDSYMBOL"}}}, "test_caching_functionality": {"test_name": "caching_functionality", "timestamp": "2025-07-09T07:37:46.769068", "success": true, "data": {"first_request_duration": 0.000119, "second_request_duration": 7e-06, "cache_effective": true, "price_consistency": true}}}, "summary": {"total_tests": 6, "successful_tests": 6, "success_rate": "100.0%", "end_time": "2025-07-09T07:37:46.769910"}}