import logging
import discord
from typing import Dict, Any, List
from datetime import datetime, timezone

from utils.config import get_guild_id

logger = logging.getLogger(__name__)

class BinanceLaunchpadHandler:
    """Handler for sending Binance launchpad alerts to Discord"""
    
    def __init__(self, bot):
        self.bot = bot
        self.guild_id = get_guild_id()
        self.channel_name = "🚨-alerts"
    
    async def handle_launchpad_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Process launchpad alerts and send to Discord"""
        try:
            if alert_type == "binance_launchpad":
                embed = await self._create_launchpad_embed(alert_data)
                if embed:
                    await self._send_to_alerts_channel(embed)
            else:
                logger.warning(f"Unknown alert type: {alert_type}")
                
        except Exception as e:
            logger.error(f"Error handling launchpad alert: {e}")
    
    async def _create_launchpad_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for launchpad announcements"""
        announcements = alert_data.get('announcements', [])
        
        if not announcements:
            return None
        
        # Create embed
        embed = discord.Embed(
            color=0xff6b35,  # Orange color
            timestamp=datetime.now(timezone.utc)
        )
        
        # Process announcements
        alert_text = ""
        for announcement in announcements[:3]:  # Limit to 3 per embed
            # Get announcement details
            project_name = announcement.get('project_name', 'Unknown')
            token_symbol = announcement.get('token_symbol', 'UNKNOWN')
            announcement_type = announcement.get('announcement_type', 'announcement')
            title = announcement.get('title', '')
            
            # Get emoji and type text
            emoji = self._get_type_emoji(announcement_type)
            type_text = self._get_type_text(announcement_type)
            
            # Format message
            alert_text += f"🚀 BINANCE LP: {emoji} **{token_symbol}** {type_text}\n"
            alert_text += f"💰 Token: {token_symbol}\n"
            alert_text += f"📝 Dự án: {project_name}\n"
            
            # Add release date if available
            if announcement.get('releaseDate'):
                release_dt = datetime.fromtimestamp(announcement['releaseDate'] / 1000, tz=timezone.utc)
                alert_text += f"📅 Thời gian: {release_dt.strftime('%Y-%m-%d %H:%M')} UTC\n"
            
            alert_text += f"🎯 Loại: {type_text}\n"
            alert_text += f"📋 Chi tiết: {title[:100]}...\n\n"
        
        embed.description = alert_text.strip()
        embed.set_footer(text="Binance Launchpad • ChartFix")
        
        return embed
    
    def _get_type_emoji(self, announcement_type: str) -> str:
        """Get emoji for announcement type"""
        emoji_map = {
            'launchpad': '🚀',
            'airdrop': '🎁',
            'earn': '💰',
            'staking': '🔒',
            'megadrop': '💎',
            'announcement': '📢'
        }
        return emoji_map.get(announcement_type, '📢')
    
    def _get_type_text(self, announcement_type: str) -> str:
        """Get display text for announcement type"""
        type_map = {
            'launchpad': 'DỰ ÁN MỚI',
            'airdrop': 'AIRDROP',
            'earn': 'EARN',
            'staking': 'STAKING',
            'megadrop': 'MEGADROP',
            'announcement': 'THÔNG BÁO'
        }
        return type_map.get(announcement_type, 'THÔNG BÁO')
    
    async def _send_to_alerts_channel(self, embed: discord.Embed):
        """Send embed to alerts channel"""
        if not embed:
            return
            
        if self.guild_id:
            try:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    # Find alerts channel
                    target_channel = None
                    for channel in guild.channels:
                        if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                            if self.channel_name.lower() in channel.name.lower():
                                target_channel = channel
                                break
                    
                    # Send alert if channel found
                    if target_channel:
                        await target_channel.send(embed=embed)
                        logger.info(f"Sent Binance launchpad alert to #{target_channel.name}")
                    else:
                        logger.warning("Alerts channel not found")
                else:
                    logger.warning(f"Guild not found: {self.guild_id}")
            except Exception as e:
                logger.error(f"Failed to send launchpad alert: {e}")
        else:
            logger.warning("No guild_id configured")

# Global handler instance
_binance_launchpad_handler = None

def get_binance_launchpad_handler(bot) -> BinanceLaunchpadHandler:
    """Get or create binance launchpad handler instance"""
    global _binance_launchpad_handler
    if _binance_launchpad_handler is None:
        _binance_launchpad_handler = BinanceLaunchpadHandler(bot)
    return _binance_launchpad_handler
