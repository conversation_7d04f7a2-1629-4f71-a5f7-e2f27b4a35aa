import discord
from discord.ext import commands
import logging
from typing import Optional, List
from datetime import datetime

from services.market.mt5_data_service import get_mt5_service, get_mt5_price, get_mt5_historical_data, get_mt5_multiple_prices
from utils.ui_components import create_embed, format_number

logger = logging.getLogger(__name__)

class MT5Commands(commands.Cog):
    """Discord commands for MT5/Exness trading data"""
    
    def __init__(self, bot):
        self.bot = bot
        self.mt5_service = get_mt5_service()
    
    @commands.command(name='mt5price', aliases=['mt5', 'forex', 'gold'])
    async def mt5_price(self, ctx, symbol: str = None):
        """
        Get real-time price for MT5/Exness trading instruments.
        
        Usage: !mt5price XAUUSD
               !mt5price EURUSD
               !gold (shortcut for XAUUSD)
               !forex GBPUSD
        """
        if not symbol:
            embed = create_embed(
                title="🏦 MT5/Exness Price Command",
                description="Get real-time prices for forex, gold, and other trading instruments",
                color=discord.Color.blue()
            )
            embed.add_field(
                name="📖 Usage",
                value="`!mt5price SYMBOL` or `!mt5 SYMBOL`\n`!gold` (for XAU/USD)\n`!forex EURUSD`",
                inline=False
            )
            embed.add_field(
                name="📊 Supported Instruments",
                value="**Forex:** EURUSD, GBPUSD, USDJPY, USDCHF, AUDUSD, USDCAD\n"
                      "**Commodities:** XAUUSD (Gold), XAGUSD (Silver), WTIUSD (Oil)\n"
                      "**Indices:** SPX500, US30, NAS100",
                inline=False
            )
            embed.add_field(
                name="💡 Examples",
                value="`!mt5price XAUUSD` - Gold price\n"
                      "`!mt5price EURUSD` - Euro/USD rate\n"
                      "`!gold` - Quick gold price",
                inline=False
            )
            await ctx.send(embed=embed)
            return
        
        # Handle shortcut commands
        if ctx.invoked_with == 'gold':
            symbol = 'XAUUSD'
        
        try:
            async with ctx.typing():
                price_data = await get_mt5_price(symbol)
            
            if not price_data.get('success'):
                embed = create_embed(
                    title="❌ Error",
                    description=f"Failed to get price for {symbol.upper()}",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="Error Details",
                    value=price_data.get('error', 'Unknown error'),
                    inline=False
                )
                await ctx.send(embed=embed)
                return
            
            # Create price embed
            current_price = price_data.get('current_price')
            change = price_data.get('change')
            change_percent = price_data.get('change_percent')
            
            # Determine color based on change
            if change and change > 0:
                color = discord.Color.green()
                change_emoji = "📈"
            elif change and change < 0:
                color = discord.Color.red()
                change_emoji = "📉"
            else:
                color = discord.Color.blue()
                change_emoji = "➡️"
            
            embed = create_embed(
                title=f"{change_emoji} {price_data.get('name', symbol.upper())}",
                description=f"Real-time price from {price_data.get('source', 'MT5')}",
                color=color
            )
            
            # Price information
            price_text = f"**${format_number(current_price)}**"
            if change and change_percent:
                change_sign = "+" if change > 0 else ""
                price_text += f"\n{change_sign}${format_number(change)} ({change_sign}{change_percent:.2f}%)"
            
            embed.add_field(
                name="💰 Current Price",
                value=price_text,
                inline=True
            )
            
            # Market data
            if price_data.get('day_high') and price_data.get('day_low'):
                embed.add_field(
                    name="📊 Day Range",
                    value=f"High: ${format_number(price_data['day_high'])}\n"
                          f"Low: ${format_number(price_data['day_low'])}",
                    inline=True
                )
            
            if price_data.get('previous_close'):
                embed.add_field(
                    name="🔄 Previous Close",
                    value=f"${format_number(price_data['previous_close'])}",
                    inline=True
                )
            
            # Market status
            market_state = price_data.get('market_state', 'Unknown')
            if market_state:
                state_emoji = "🟢" if market_state == "REGULAR" else "🔴" if market_state == "CLOSED" else "🟡"
                embed.add_field(
                    name="🏪 Market Status",
                    value=f"{state_emoji} {market_state.title()}",
                    inline=True
                )
            
            # Volume if available
            if price_data.get('volume'):
                embed.add_field(
                    name="📈 Volume",
                    value=format_number(price_data['volume']),
                    inline=True
                )
            
            # Timezone and timestamp
            if price_data.get('timezone'):
                embed.add_field(
                    name="🌍 Timezone",
                    value=price_data['timezone'],
                    inline=True
                )
            
            embed.set_footer(text=f"Symbol: {price_data.get('symbol')} • Updated: {datetime.now().strftime('%H:%M:%S')}")
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in mt5_price command: {e}")
            embed = create_embed(
                title="❌ Command Error",
                description="An error occurred while fetching price data",
                color=discord.Color.red()
            )
            embed.add_field(name="Error", value=str(e), inline=False)
            await ctx.send(embed=embed)
    
    @commands.command(name='mt5multi', aliases=['forexmulti'])
    async def mt5_multiple_prices(self, ctx, *symbols):
        """
        Get prices for multiple MT5 instruments at once.
        
        Usage: !mt5multi XAUUSD EURUSD GBPUSD
        """
        if not symbols:
            embed = create_embed(
                title="📊 MT5 Multiple Prices",
                description="Get prices for multiple trading instruments",
                color=discord.Color.blue()
            )
            embed.add_field(
                name="Usage",
                value="`!mt5multi SYMBOL1 SYMBOL2 SYMBOL3...`",
                inline=False
            )
            embed.add_field(
                name="Example",
                value="`!mt5multi XAUUSD EURUSD GBPUSD USDJPY`",
                inline=False
            )
            await ctx.send(embed=embed)
            return
        
        if len(symbols) > 10:
            embed = create_embed(
                title="⚠️ Too Many Symbols",
                description="Please limit to 10 symbols maximum",
                color=discord.Color.orange()
            )
            await ctx.send(embed=embed)
            return
        
        try:
            async with ctx.typing():
                multi_data = await get_mt5_multiple_prices(list(symbols))
            
            if not multi_data.get('success'):
                embed = create_embed(
                    title="❌ Error",
                    description="Failed to fetch multiple prices",
                    color=discord.Color.red()
                )
                await ctx.send(embed=embed)
                return
            
            embed = create_embed(
                title="📊 MT5 Multiple Prices",
                description=f"Fetched {multi_data.get('successful_fetches', 0)}/{multi_data.get('total_symbols', 0)} prices",
                color=discord.Color.blue()
            )
            
            prices = multi_data.get('prices', {})
            
            for symbol in symbols:
                if symbol in prices:
                    price_data = prices[symbol]
                    if price_data.get('success'):
                        current_price = price_data.get('current_price')
                        change_percent = price_data.get('change_percent')
                        
                        # Format price display
                        price_text = f"${format_number(current_price)}"
                        if change_percent:
                            change_sign = "+" if change_percent > 0 else ""
                            emoji = "🟢" if change_percent > 0 else "🔴" if change_percent < 0 else "⚪"
                            price_text += f" {emoji} {change_sign}{change_percent:.2f}%"
                        
                        embed.add_field(
                            name=f"{price_data.get('name', symbol)}",
                            value=price_text,
                            inline=True
                        )
                    else:
                        embed.add_field(
                            name=f"{symbol}",
                            value=f"❌ {price_data.get('error', 'Failed')}",
                            inline=True
                        )
            
            embed.set_footer(text=f"Updated: {datetime.now().strftime('%H:%M:%S')}")
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in mt5_multiple_prices command: {e}")
            embed = create_embed(
                title="❌ Command Error",
                description="An error occurred while fetching multiple prices",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='mt5hist', aliases=['forexhist'])
    async def mt5_historical(self, ctx, symbol: str = None, interval: str = '1h', period: str = '5d'):
        """
        Get historical data for MT5 instruments.
        
        Usage: !mt5hist XAUUSD 1h 5d
        Intervals: 1m, 5m, 15m, 1h, 1d
        Periods: 1d, 5d, 1mo, 3mo, 6mo, 1y
        """
        if not symbol:
            embed = create_embed(
                title="📈 MT5 Historical Data",
                description="Get historical OHLCV data for trading instruments",
                color=discord.Color.blue()
            )
            embed.add_field(
                name="Usage",
                value="`!mt5hist SYMBOL [INTERVAL] [PERIOD]`",
                inline=False
            )
            embed.add_field(
                name="Intervals",
                value="`1m`, `5m`, `15m`, `1h`, `1d`",
                inline=True
            )
            embed.add_field(
                name="Periods",
                value="`1d`, `5d`, `1mo`, `3mo`, `6mo`, `1y`",
                inline=True
            )
            embed.add_field(
                name="Example",
                value="`!mt5hist XAUUSD 1h 5d`",
                inline=False
            )
            await ctx.send(embed=embed)
            return
        
        try:
            async with ctx.typing():
                hist_data = await get_mt5_historical_data(symbol, interval, period)
            
            if not hist_data.get('success'):
                embed = create_embed(
                    title="❌ Error",
                    description=f"Failed to get historical data for {symbol.upper()}",
                    color=discord.Color.red()
                )
                embed.add_field(
                    name="Error Details",
                    value=hist_data.get('error', 'Unknown error'),
                    inline=False
                )
                await ctx.send(embed=embed)
                return
            
            data_points = hist_data.get('data', [])
            if not data_points:
                embed = create_embed(
                    title="📈 No Data",
                    description=f"No historical data available for {symbol.upper()}",
                    color=discord.Color.orange()
                )
                await ctx.send(embed=embed)
                return
            
            # Get latest data point
            latest = data_points[-1]
            
            embed = create_embed(
                title=f"📈 {hist_data.get('symbol')} Historical Data",
                description=f"Interval: {interval} • Period: {period} • Data Points: {len(data_points)}",
                color=discord.Color.blue()
            )
            
            # Latest OHLC
            embed.add_field(
                name="📊 Latest OHLC",
                value=f"Open: ${format_number(latest.get('open', 0))}\n"
                      f"High: ${format_number(latest.get('high', 0))}\n"
                      f"Low: ${format_number(latest.get('low', 0))}\n"
                      f"Close: ${format_number(latest.get('close', 0))}",
                inline=True
            )
            
            # Volume if available
            if latest.get('volume'):
                embed.add_field(
                    name="📈 Volume",
                    value=format_number(latest.get('volume')),
                    inline=True
                )
            
            # Time range
            if len(data_points) > 1:
                first_time = data_points[0].get('timestamp', '')
                last_time = data_points[-1].get('timestamp', '')
                embed.add_field(
                    name="⏰ Time Range",
                    value=f"From: {first_time[:16] if first_time else 'N/A'}\n"
                          f"To: {last_time[:16] if last_time else 'N/A'}",
                    inline=True
                )
            
            embed.set_footer(text=f"Source: {hist_data.get('source', 'MT5')} • Updated: {datetime.now().strftime('%H:%M:%S')}")
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in mt5_historical command: {e}")
            embed = create_embed(
                title="❌ Command Error",
                description="An error occurred while fetching historical data",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='mt5list', aliases=['forexlist'])
    async def mt5_supported_instruments(self, ctx):
        """List all supported MT5/Exness trading instruments"""
        try:
            instruments = self.mt5_service.get_supported_instruments()
            
            embed = create_embed(
                title="🏦 Supported MT5/Exness Instruments",
                description=f"Total: {len(instruments)} trading instruments",
                color=discord.Color.blue()
            )
            
            # Group by type
            forex_instruments = [i for i in instruments if i['type'] == 'forex']
            commodity_instruments = [i for i in instruments if i['type'] == 'commodity']
            index_instruments = [i for i in instruments if i['type'] == 'index']
            
            if forex_instruments:
                forex_list = '\n'.join([f"`{i['symbol']}` - {i['name']}" for i in forex_instruments[:8]])
                if len(forex_instruments) > 8:
                    forex_list += f"\n... and {len(forex_instruments) - 8} more"
                embed.add_field(
                    name="💱 Forex Pairs",
                    value=forex_list,
                    inline=False
                )
            
            if commodity_instruments:
                commodity_list = '\n'.join([f"`{i['symbol']}` - {i['name']}" for i in commodity_instruments])
                embed.add_field(
                    name="🥇 Commodities",
                    value=commodity_list,
                    inline=False
                )
            
            if index_instruments:
                index_list = '\n'.join([f"`{i['symbol']}` - {i['name']}" for i in index_instruments])
                embed.add_field(
                    name="📊 Indices",
                    value=index_list,
                    inline=False
                )
            
            embed.add_field(
                name="💡 Usage",
                value="Use `!mt5price SYMBOL` to get real-time prices\n"
                      "Use `!mt5multi SYMBOL1 SYMBOL2...` for multiple prices",
                inline=False
            )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in mt5_supported_instruments command: {e}")
            embed = create_embed(
                title="❌ Command Error",
                description="An error occurred while fetching supported instruments",
                color=discord.Color.red()
            )
            await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(MT5Commands(bot))
