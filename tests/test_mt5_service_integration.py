#!/usr/bin/env python3

import asyncio
import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# Add parent directory to path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.market.mt5_data_service import get_mt5_service, get_mt5_price, get_mt5_historical_data, get_mt5_multiple_prices

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MT5ServiceIntegrationTest:
    """Integration test for MT5 Data Service"""
    
    def __init__(self):
        self.service = get_mt5_service()
        self.test_symbols = ['XAUUSD', 'EURUSD', 'GBPUSD', 'USDJPY']
    
    async def test_service_initialization(self) -> Dict[str, Any]:
        """Test service initialization"""
        logger.info("Testing MT5 service initialization...")
        
        result = {
            'test_name': 'service_initialization',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # Test service instance
            assert self.service is not None, "Service instance is None"
            
            # Test supported instruments
            instruments = self.service.get_supported_instruments()
            assert len(instruments) > 0, "No supported instruments found"
            
            # Test symbol normalization
            normalized = self.service.normalize_symbol('XAU/USD')
            assert normalized == 'XAUUSD', f"Symbol normalization failed: {normalized}"
            
            # Test Yahoo symbol mapping
            yahoo_symbol = self.service.get_yahoo_symbol('XAUUSD')
            assert yahoo_symbol == 'GC=F', f"Yahoo symbol mapping failed: {yahoo_symbol}"
            
            result['data'] = {
                'supported_instruments': len(instruments),
                'sample_instruments': instruments[:5],
                'symbol_normalization_test': normalized,
                'yahoo_mapping_test': yahoo_symbol
            }
            result['success'] = True
            logger.info("Service initialization test passed")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Service initialization test failed: {e}")
        
        return result
    
    async def test_real_time_price_retrieval(self) -> Dict[str, Any]:
        """Test real-time price retrieval"""
        logger.info("Testing real-time price retrieval...")
        
        result = {
            'test_name': 'real_time_price_retrieval',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # Test single symbol
            price_data = await self.service.get_real_time_price('XAUUSD')
            
            assert price_data.get('success'), f"Price retrieval failed: {price_data.get('error')}"
            assert price_data.get('current_price') is not None, "No current price returned"
            assert price_data.get('symbol') == 'XAUUSD', f"Wrong symbol returned: {price_data.get('symbol')}"
            
            result['data'] = {
                'symbol': price_data.get('symbol'),
                'current_price': price_data.get('current_price'),
                'currency': price_data.get('currency'),
                'change': price_data.get('change'),
                'change_percent': price_data.get('change_percent'),
                'market_state': price_data.get('market_state'),
                'source': price_data.get('source')
            }
            result['success'] = True
            logger.info(f"Real-time price test passed: XAUUSD = ${price_data.get('current_price')}")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Real-time price test failed: {e}")
        
        return result
    
    async def test_multiple_prices(self) -> Dict[str, Any]:
        """Test multiple price retrieval"""
        logger.info("Testing multiple price retrieval...")
        
        result = {
            'test_name': 'multiple_prices',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # Test multiple symbols
            multi_data = await self.service.get_multiple_prices(self.test_symbols)
            
            assert multi_data.get('success'), "Multiple price retrieval failed"
            assert multi_data.get('total_symbols') == len(self.test_symbols), "Wrong number of symbols"
            
            prices = multi_data.get('prices', {})
            successful_count = 0
            price_summary = {}
            
            for symbol in self.test_symbols:
                if symbol in prices and prices[symbol].get('success'):
                    successful_count += 1
                    price_summary[symbol] = {
                        'price': prices[symbol].get('current_price'),
                        'change_percent': prices[symbol].get('change_percent')
                    }
            
            result['data'] = {
                'total_symbols': len(self.test_symbols),
                'successful_fetches': successful_count,
                'success_rate': f"{(successful_count/len(self.test_symbols))*100:.1f}%",
                'price_summary': price_summary
            }
            result['success'] = successful_count > 0
            logger.info(f"Multiple prices test: {successful_count}/{len(self.test_symbols)} successful")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Multiple prices test failed: {e}")
        
        return result
    
    async def test_historical_data(self) -> Dict[str, Any]:
        """Test historical data retrieval"""
        logger.info("Testing historical data retrieval...")
        
        result = {
            'test_name': 'historical_data',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # Test historical data
            hist_data = await self.service.get_historical_data('XAUUSD', '1h', '5d')
            
            assert hist_data.get('success'), f"Historical data retrieval failed: {hist_data.get('error')}"
            assert hist_data.get('data_points', 0) > 0, "No historical data points returned"
            
            data_points = hist_data.get('data', [])
            sample_data = data_points[-5:] if len(data_points) >= 5 else data_points
            
            result['data'] = {
                'symbol': hist_data.get('symbol'),
                'interval': hist_data.get('interval'),
                'period': hist_data.get('period'),
                'total_data_points': hist_data.get('data_points'),
                'sample_data': sample_data,
                'source': hist_data.get('source')
            }
            result['success'] = True
            logger.info(f"Historical data test passed: {hist_data.get('data_points')} data points")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Historical data test failed: {e}")
        
        return result
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling for invalid symbols"""
        logger.info("Testing error handling...")
        
        result = {
            'test_name': 'error_handling',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # Test invalid symbol
            invalid_data = await self.service.get_real_time_price('INVALID_SYMBOL')
            
            assert not invalid_data.get('success'), "Invalid symbol should fail"
            assert 'error' in invalid_data, "Error message should be present"
            
            result['data'] = {
                'invalid_symbol_response': {
                    'success': invalid_data.get('success'),
                    'error': invalid_data.get('error'),
                    'symbol': invalid_data.get('symbol')
                }
            }
            result['success'] = True
            logger.info("Error handling test passed")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Error handling test failed: {e}")
        
        return result
    
    async def test_caching_functionality(self) -> Dict[str, Any]:
        """Test caching functionality"""
        logger.info("Testing caching functionality...")
        
        result = {
            'test_name': 'caching_functionality',
            'timestamp': datetime.now().isoformat(),
            'success': False
        }
        
        try:
            # First request (should hit API)
            start_time = datetime.now()
            first_data = await self.service.get_real_time_price('EURUSD')
            first_duration = (datetime.now() - start_time).total_seconds()
            
            # Second request (should hit cache)
            start_time = datetime.now()
            second_data = await self.service.get_real_time_price('EURUSD')
            second_duration = (datetime.now() - start_time).total_seconds()
            
            assert first_data.get('success'), "First request failed"
            assert second_data.get('success'), "Second request failed"
            
            # Cache should make second request faster
            cache_effective = second_duration < first_duration
            
            result['data'] = {
                'first_request_duration': first_duration,
                'second_request_duration': second_duration,
                'cache_effective': cache_effective,
                'price_consistency': first_data.get('current_price') == second_data.get('current_price')
            }
            result['success'] = True
            logger.info(f"Caching test: First={first_duration:.3f}s, Second={second_duration:.3f}s")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Caching test failed: {e}")
        
        return result
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        logger.info("Starting comprehensive MT5 service integration tests...")
        
        all_results = {
            'test_suite': 'MT5_Service_Integration_Tests',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Run all tests
        test_methods = [
            self.test_service_initialization,
            self.test_real_time_price_retrieval,
            self.test_multiple_prices,
            self.test_historical_data,
            self.test_error_handling,
            self.test_caching_functionality
        ]
        
        for test_method in test_methods:
            test_name = test_method.__name__
            try:
                all_results['tests'][test_name] = await test_method()
            except Exception as e:
                all_results['tests'][test_name] = {
                    'test_name': test_name,
                    'success': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
        # Compile summary
        total_tests = len(all_results['tests'])
        successful_tests = sum(1 for test in all_results['tests'].values() if test.get('success', False))
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'end_time': datetime.now().isoformat()
        }
        
        logger.info(f"Integration test suite completed: {successful_tests}/{total_tests} tests passed")
        return all_results

async def main():
    """Main test execution function"""
    test_suite = MT5ServiceIntegrationTest()
    
    # Run comprehensive tests
    results = await test_suite.run_comprehensive_tests()
    
    # Save results to file
    with open('mt5_service_integration_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("Test results saved to mt5_service_integration_results.json")
    
    # Print summary
    print("\n" + "="*70)
    print("MT5 SERVICE INTEGRATION TEST SUMMARY")
    print("="*70)
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Successful: {results['summary']['successful_tests']}")
    print(f"Success Rate: {results['summary']['success_rate']}")
    print("="*70)
    
    # Print individual test results
    for test_name, test_result in results['tests'].items():
        status = "✅ PASS" if test_result.get('success') else "❌ FAIL"
        print(f"{status} {test_name}")
        if not test_result.get('success') and 'error' in test_result:
            print(f"    Error: {test_result['error']}")
        elif test_result.get('success') and 'data' in test_result:
            # Print key data points
            data = test_result['data']
            if 'current_price' in data:
                print(f"    Price: ${data['current_price']}")
            elif 'successful_fetches' in data:
                print(f"    Success Rate: {data.get('success_rate', 'N/A')}")
            elif 'total_data_points' in data:
                print(f"    Data Points: {data['total_data_points']}")

if __name__ == "__main__":
    asyncio.run(main())
