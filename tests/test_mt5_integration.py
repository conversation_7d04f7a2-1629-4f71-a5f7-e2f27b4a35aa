#!/usr/bin/env python3

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add parent directory to path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.market.mt5_data_service import get_mt5_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_mt5_integration():
    """Test MT5 service integration with existing system"""
    logger.info("Testing MT5 service integration...")
    
    try:
        # Test service initialization
        mt5_service = get_mt5_service()
        logger.info("✅ MT5 service initialized successfully")
        
        # Test supported instruments
        instruments = mt5_service.get_supported_instruments()
        logger.info(f"✅ Found {len(instruments)} supported instruments")
        
        # Test real-time price for gold
        logger.info("Testing real-time gold price...")
        gold_price = await mt5_service.get_real_time_price('XAUUSD')
        
        if gold_price.get('success'):
            logger.info(f"✅ Gold price: ${gold_price.get('current_price')}")
        else:
            logger.error(f"❌ Gold price failed: {gold_price.get('error')}")
        
        # Test forex price
        logger.info("Testing EUR/USD price...")
        eur_price = await mt5_service.get_real_time_price('EURUSD')
        
        if eur_price.get('success'):
            logger.info(f"✅ EUR/USD price: ${eur_price.get('current_price')}")
        else:
            logger.error(f"❌ EUR/USD price failed: {eur_price.get('error')}")
        
        # Test multiple prices
        logger.info("Testing multiple prices...")
        multi_prices = await mt5_service.get_multiple_prices(['XAUUSD', 'EURUSD', 'GBPUSD'])
        
        if multi_prices.get('success'):
            successful = multi_prices.get('successful_fetches', 0)
            total = multi_prices.get('total_symbols', 0)
            logger.info(f"✅ Multiple prices: {successful}/{total} successful")
        else:
            logger.error("❌ Multiple prices failed")
        
        # Test historical data
        logger.info("Testing historical data...")
        hist_data = await mt5_service.get_historical_data('XAUUSD', '1h', '1d')
        
        if hist_data.get('success'):
            data_points = hist_data.get('data_points', 0)
            logger.info(f"✅ Historical data: {data_points} data points")
        else:
            logger.error(f"❌ Historical data failed: {hist_data.get('error')}")
        
        logger.info("🎉 MT5 integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ MT5 integration test failed: {e}")
        return False

async def test_command_imports():
    """Test that MT5 commands can be imported"""
    logger.info("Testing MT5 command imports...")
    
    try:
        # Test importing the commands module
        from handlers.discord.market.mt5_commands import MT5Commands
        logger.info("✅ MT5Commands class imported successfully")
        
        # Test that required methods exist
        required_methods = ['mt5_price', 'mt5_multiple_prices', 'mt5_historical', 'mt5_supported_instruments']
        
        for method_name in required_methods:
            if hasattr(MT5Commands, method_name):
                logger.info(f"✅ Method {method_name} exists")
            else:
                logger.error(f"❌ Method {method_name} missing")
                return False
        
        logger.info("🎉 Command import test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Command import test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("="*60)
    logger.info("MT5 INTEGRATION TEST SUITE")
    logger.info("="*60)
    
    # Test 1: Service integration
    service_test = await test_mt5_integration()
    
    # Test 2: Command imports
    command_test = await test_command_imports()
    
    # Summary
    logger.info("="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    logger.info(f"Service Integration: {'✅ PASS' if service_test else '❌ FAIL'}")
    logger.info(f"Command Imports: {'✅ PASS' if command_test else '❌ FAIL'}")
    
    if service_test and command_test:
        logger.info("🎉 ALL TESTS PASSED - MT5 integration is ready!")
        logger.info("")
        logger.info("Available Discord Commands:")
        logger.info("  !mt5price XAUUSD    - Get gold price")
        logger.info("  !mt5price EURUSD    - Get EUR/USD rate")
        logger.info("  !gold               - Quick gold price")
        logger.info("  !forex GBPUSD       - Get GBP/USD rate")
        logger.info("  !mt5multi XAUUSD EURUSD GBPUSD - Multiple prices")
        logger.info("  !mt5hist XAUUSD 1h 5d - Historical data")
        logger.info("  !mt5list            - List supported instruments")
        logger.info("")
        logger.info("The MT5 module is now integrated and ready for use!")
    else:
        logger.error("❌ SOME TESTS FAILED - Please check the errors above")
    
    logger.info("="*60)

if __name__ == "__main__":
    asyncio.run(main())
