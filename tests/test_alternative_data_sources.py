#!/usr/bin/env python3

import asyncio
import aiohttp
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlternativeDataSourceTest:
    """
    Test alternative data sources for MT5/Exness-like trading data.
    Tests various free APIs for forex, gold, and commodity data.
    """
    
    def __init__(self):
        self.session = None
        self.test_results = {}
        
    async def setup_session(self):
        """Setup HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'ChartFix-MT5-Test/1.0'}
            )
    
    async def cleanup_session(self):
        """Cleanup HTTP session"""
        if self.session:
            await self.session.close()
    
    async def test_fixer_io_api(self) -> Dict[str, Any]:
        """Test Fixer.io API for forex rates"""
        logger.info("Testing Fixer.io API for forex data...")
        results = {
            'test_name': 'fixer_io_forex',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Free tier endpoint (limited but functional for testing)
            url = "http://data.fixer.io/api/latest"
            params = {
                'access_key': 'demo_key',  # Replace with actual free key
                'symbols': 'USD,EUR,GBP,JPY',
                'base': 'EUR'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        results['data'] = {
                            'base': data.get('base'),
                            'date': data.get('date'),
                            'rates': data.get('rates', {}),
                            'timestamp': data.get('timestamp')
                        }
                        results['success'] = True
                        logger.info("Fixer.io API test successful")
                    else:
                        results['error'] = data.get('error', {}).get('info', 'Unknown error')
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Fixer.io API test failed: {e}")
        
        return results
    
    async def test_metals_api(self) -> Dict[str, Any]:
        """Test Metals-API for gold/precious metals data"""
        logger.info("Testing Metals-API for gold data...")
        results = {
            'test_name': 'metals_api_gold',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Free tier endpoint
            url = "https://api.metals.live/v1/spot"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Extract gold data
                    gold_data = {}
                    for metal in data:
                        if metal.get('metal') == 'XAU':
                            gold_data = {
                                'metal': metal.get('metal'),
                                'currency': metal.get('currency'),
                                'price': metal.get('price'),
                                'timestamp': metal.get('timestamp'),
                                'change': metal.get('change'),
                                'change_percent': metal.get('change_percent')
                            }
                            break
                    
                    if gold_data:
                        results['data'] = gold_data
                        results['success'] = True
                        logger.info(f"Gold price: ${gold_data.get('price', 'N/A')}")
                    else:
                        results['error'] = "No gold data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Metals-API test failed: {e}")
        
        return results
    
    async def test_alpha_vantage_forex(self) -> Dict[str, Any]:
        """Test Alpha Vantage API for forex data"""
        logger.info("Testing Alpha Vantage API for forex data...")
        results = {
            'test_name': 'alpha_vantage_forex',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Free tier endpoint
            url = "https://www.alphavantage.co/query"
            params = {
                'function': 'CURRENCY_EXCHANGE_RATE',
                'from_currency': 'EUR',
                'to_currency': 'USD',
                'apikey': 'demo'  # Replace with actual free key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    exchange_rate = data.get('Realtime Currency Exchange Rate', {})
                    if exchange_rate:
                        results['data'] = {
                            'from_currency': exchange_rate.get('1. From_Currency Code'),
                            'to_currency': exchange_rate.get('3. To_Currency Code'),
                            'exchange_rate': float(exchange_rate.get('5. Exchange Rate', 0)),
                            'last_refreshed': exchange_rate.get('6. Last Refreshed'),
                            'timezone': exchange_rate.get('7. Time Zone'),
                            'bid_price': float(exchange_rate.get('8. Bid Price', 0)),
                            'ask_price': float(exchange_rate.get('9. Ask Price', 0))
                        }
                        results['success'] = True
                        logger.info(f"EUR/USD rate: {results['data']['exchange_rate']}")
                    else:
                        results['error'] = "No exchange rate data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Alpha Vantage API test failed: {e}")
        
        return results
    
    async def test_yahoo_finance_api(self) -> Dict[str, Any]:
        """Test Yahoo Finance API for market data"""
        logger.info("Testing Yahoo Finance API for market data...")
        results = {
            'test_name': 'yahoo_finance_api',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Yahoo Finance API endpoint (unofficial but widely used)
            symbols = ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'GC=F']  # GC=F is gold futures
            url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbols[0]}"
            
            params = {
                'interval': '1m',
                'range': '1d'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        meta = result.get('meta', {})
                        
                        results['data'] = {
                            'symbol': meta.get('symbol'),
                            'currency': meta.get('currency'),
                            'exchange_name': meta.get('exchangeName'),
                            'current_price': meta.get('regularMarketPrice'),
                            'previous_close': meta.get('previousClose'),
                            'day_high': meta.get('regularMarketDayHigh'),
                            'day_low': meta.get('regularMarketDayLow'),
                            'timezone': meta.get('timezone')
                        }
                        results['success'] = True
                        logger.info(f"Yahoo Finance data for {meta.get('symbol')}: ${meta.get('regularMarketPrice')}")
                    else:
                        results['error'] = "No chart data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Yahoo Finance API test failed: {e}")
        
        return results
    
    async def test_exchangerate_api(self) -> Dict[str, Any]:
        """Test ExchangeRate-API for forex data"""
        logger.info("Testing ExchangeRate-API for forex data...")
        results = {
            'test_name': 'exchangerate_api',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Free tier endpoint
            url = "https://api.exchangerate-api.com/v4/latest/USD"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results['data'] = {
                        'base': data.get('base'),
                        'date': data.get('date'),
                        'rates': {
                            'EUR': data.get('rates', {}).get('EUR'),
                            'GBP': data.get('rates', {}).get('GBP'),
                            'JPY': data.get('rates', {}).get('JPY'),
                            'CHF': data.get('rates', {}).get('CHF'),
                            'CAD': data.get('rates', {}).get('CAD'),
                            'AUD': data.get('rates', {}).get('AUD')
                        }
                    }
                    results['success'] = True
                    logger.info(f"ExchangeRate-API test successful, base: {data.get('base')}")
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"ExchangeRate-API test failed: {e}")
        
        return results
    
    async def test_data_reliability(self, iterations: int = 3) -> Dict[str, Any]:
        """Test data reliability across multiple requests"""
        logger.info(f"Testing data reliability over {iterations} iterations...")
        results = {
            'test_name': 'data_reliability',
            'iterations': iterations,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            reliability_data = []
            
            for i in range(iterations):
                logger.info(f"Reliability test iteration {i+1}/{iterations}")
                
                # Test multiple APIs
                yahoo_result = await self.test_yahoo_finance_api()
                exchange_result = await self.test_exchangerate_api()
                
                iteration_data = {
                    'iteration': i + 1,
                    'timestamp': datetime.now().isoformat(),
                    'yahoo_success': yahoo_result.get('success', False),
                    'exchange_success': exchange_result.get('success', False),
                    'yahoo_data': yahoo_result.get('data', {}),
                    'exchange_data': exchange_result.get('data', {})
                }
                
                reliability_data.append(iteration_data)
                
                if i < iterations - 1:
                    await asyncio.sleep(5)  # Wait between iterations
            
            # Calculate reliability metrics
            yahoo_success_rate = sum(1 for d in reliability_data if d['yahoo_success']) / iterations
            exchange_success_rate = sum(1 for d in reliability_data if d['exchange_success']) / iterations
            
            results['data'] = {
                'iterations_data': reliability_data,
                'yahoo_success_rate': f"{yahoo_success_rate*100:.1f}%",
                'exchange_success_rate': f"{exchange_success_rate*100:.1f}%",
                'overall_reliability': f"{((yahoo_success_rate + exchange_success_rate)/2)*100:.1f}%"
            }
            results['success'] = True
            logger.info(f"Reliability test completed: {results['data']['overall_reliability']} overall reliability")
            
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Data reliability test failed: {e}")
        
        return results
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all alternative data source tests"""
        logger.info("Starting comprehensive alternative data source tests...")
        
        all_results = {
            'test_suite': 'Alternative_Data_Sources_Comprehensive',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Run all tests
        test_functions = [
            self.test_yahoo_finance_api,
            self.test_exchangerate_api,
            self.test_metals_api,
            # self.test_fixer_io_api,  # Requires API key
            # self.test_alpha_vantage_forex,  # Requires API key
        ]
        
        for test_func in test_functions:
            test_name = test_func.__name__
            all_results['tests'][test_name] = await test_func()
        
        # Run reliability test
        all_results['tests']['data_reliability'] = await self.test_data_reliability(2)
        
        # Compile summary
        total_tests = len(all_results['tests'])
        successful_tests = sum(1 for test in all_results['tests'].values() if test.get('success', False))
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'end_time': datetime.now().isoformat()
        }
        
        await self.cleanup_session()
        logger.info(f"Test suite completed: {successful_tests}/{total_tests} tests passed")
        return all_results

async def main():
    """Main test execution function"""
    test_suite = AlternativeDataSourceTest()
    
    # Run comprehensive tests
    results = await test_suite.run_comprehensive_tests()
    
    # Save results to file
    with open('alternative_data_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("Test results saved to alternative_data_test_results.json")
    
    # Print summary
    print("\n" + "="*60)
    print("ALTERNATIVE DATA SOURCES TEST SUMMARY")
    print("="*60)
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Successful: {results['summary']['successful_tests']}")
    print(f"Success Rate: {results['summary']['success_rate']}")
    print("="*60)
    
    # Print individual test results
    for test_name, test_result in results['tests'].items():
        status = "✅ PASS" if test_result.get('success') else "❌ FAIL"
        print(f"{status} {test_name}")
        if not test_result.get('success') and 'error' in test_result:
            print(f"    Error: {test_result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
