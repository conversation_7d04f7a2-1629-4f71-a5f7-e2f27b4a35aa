#!/usr/bin/env python3

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import v20
from v20.context import Context
from v20.request import Request

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MT5DataRetrievalTest:
    """
    Comprehensive test suite for MT5/Exness data retrieval using OANDA API as alternative.
    Tests data accuracy, reliability, and format consistency for trading instruments.
    """
    
    def __init__(self):
        self.oanda_api = None
        self.test_results = {}
        self.test_symbols = [
            'XAU_USD',  # Gold
            'EUR_USD',  # Major forex pair
            'GBP_USD',  # Major forex pair
            'USD_JPY',  # Major forex pair
            'SPX500_USD',  # S&P 500 index
            'US30_USD',  # Dow Jones
            'WTICO_USD',  # Oil
        ]
        
    def setup_oanda_connection(self, api_token: str = None, account_id: str = None, environment: str = "practice"):
        """
        Setup OANDA API connection for testing.
        
        Args:
            api_token: OANDA API token (demo token for testing)
            account_id: OANDA account ID
            environment: 'practice' for demo, 'live' for real trading
        """
        try:
            # Demo credentials for testing (replace with actual demo account)
            demo_token = api_token or "demo_token_here"
            demo_account = account_id or "demo_account_here"
            
            self.oanda_api = Context(
                hostname="api-fxpractice.oanda.com" if environment == "practice" else "api-fxtrade.oanda.com",
                token=demo_token,
                port=443,
                ssl=True,
                application="ChartFix-MT5-Test"
            )
            
            self.account_id = demo_account
            logger.info(f"OANDA API connection setup complete for {environment} environment")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup OANDA connection: {e}")
            return False
    
    async def test_instrument_availability(self) -> Dict[str, Any]:
        """Test availability of trading instruments"""
        logger.info("Testing instrument availability...")
        results = {
            'test_name': 'instrument_availability',
            'timestamp': datetime.now().isoformat(),
            'results': {},
            'success': False
        }
        
        try:
            if not self.oanda_api:
                results['error'] = "OANDA API not initialized"
                return results
            
            # Get available instruments
            request = Request(
                method="GET",
                path=f"/v3/accounts/{self.account_id}/instruments"
            )
            
            response = self.oanda_api.request(request)
            
            if response.status == 200:
                instruments = response.body.get('instruments', [])
                available_symbols = {}
                
                for symbol in self.test_symbols:
                    found = any(inst['name'] == symbol for inst in instruments)
                    available_symbols[symbol] = {
                        'available': found,
                        'details': next((inst for inst in instruments if inst['name'] == symbol), None)
                    }
                
                results['results'] = available_symbols
                results['total_instruments'] = len(instruments)
                results['success'] = True
                logger.info(f"Found {len(instruments)} available instruments")
                
            else:
                results['error'] = f"API request failed with status {response.status}"
                
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Instrument availability test failed: {e}")
        
        return results
    
    async def test_real_time_pricing(self, symbol: str = 'XAU_USD') -> Dict[str, Any]:
        """Test real-time pricing data retrieval"""
        logger.info(f"Testing real-time pricing for {symbol}...")
        results = {
            'test_name': 'real_time_pricing',
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'results': {},
            'success': False
        }
        
        try:
            if not self.oanda_api:
                results['error'] = "OANDA API not initialized"
                return results
            
            # Get current pricing
            request = Request(
                method="GET",
                path=f"/v3/accounts/{self.account_id}/pricing",
                params={'instruments': symbol}
            )
            
            response = self.oanda_api.request(request)
            
            if response.status == 200:
                prices = response.body.get('prices', [])
                if prices:
                    price_data = prices[0]
                    results['results'] = {
                        'instrument': price_data.get('instrument'),
                        'time': price_data.get('time'),
                        'bid': float(price_data.get('bids', [{}])[0].get('price', 0)),
                        'ask': float(price_data.get('asks', [{}])[0].get('price', 0)),
                        'spread': float(price_data.get('asks', [{}])[0].get('price', 0)) - float(price_data.get('bids', [{}])[0].get('price', 0)),
                        'status': price_data.get('status'),
                        'tradeable': price_data.get('tradeable')
                    }
                    results['success'] = True
                    logger.info(f"Real-time price for {symbol}: Bid={results['results']['bid']}, Ask={results['results']['ask']}")
                else:
                    results['error'] = "No price data returned"
            else:
                results['error'] = f"API request failed with status {response.status}"
                
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Real-time pricing test failed: {e}")
        
        return results
    
    async def test_historical_data(self, symbol: str = 'XAU_USD', granularity: str = 'M1', count: int = 100) -> Dict[str, Any]:
        """Test historical data retrieval"""
        logger.info(f"Testing historical data for {symbol} with {granularity} granularity...")
        results = {
            'test_name': 'historical_data',
            'symbol': symbol,
            'granularity': granularity,
            'timestamp': datetime.now().isoformat(),
            'results': {},
            'success': False
        }
        
        try:
            if not self.oanda_api:
                results['error'] = "OANDA API not initialized"
                return results
            
            # Get historical candles
            request = Request(
                method="GET",
                path=f"/v3/instruments/{symbol}/candles",
                params={
                    'granularity': granularity,
                    'count': count
                }
            )
            
            response = self.oanda_api.request(request)
            
            if response.status == 200:
                candles = response.body.get('candles', [])
                if candles:
                    # Process candle data
                    processed_candles = []
                    for candle in candles[-10:]:  # Last 10 candles for testing
                        if candle.get('complete'):
                            mid = candle.get('mid', {})
                            processed_candles.append({
                                'time': candle.get('time'),
                                'open': float(mid.get('o', 0)),
                                'high': float(mid.get('h', 0)),
                                'low': float(mid.get('l', 0)),
                                'close': float(mid.get('c', 0)),
                                'volume': int(candle.get('volume', 0))
                            })
                    
                    results['results'] = {
                        'total_candles': len(candles),
                        'complete_candles': len([c for c in candles if c.get('complete')]),
                        'sample_data': processed_candles,
                        'data_range': {
                            'from': candles[0].get('time') if candles else None,
                            'to': candles[-1].get('time') if candles else None
                        }
                    }
                    results['success'] = True
                    logger.info(f"Retrieved {len(candles)} historical candles for {symbol}")
                else:
                    results['error'] = "No historical data returned"
            else:
                results['error'] = f"API request failed with status {response.status}"
                
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Historical data test failed: {e}")
        
        return results
    
    async def test_data_consistency(self, symbol: str = 'XAU_USD', iterations: int = 5) -> Dict[str, Any]:
        """Test data consistency across multiple requests"""
        logger.info(f"Testing data consistency for {symbol} over {iterations} iterations...")
        results = {
            'test_name': 'data_consistency',
            'symbol': symbol,
            'iterations': iterations,
            'timestamp': datetime.now().isoformat(),
            'results': {},
            'success': False
        }
        
        try:
            prices = []
            timestamps = []
            
            for i in range(iterations):
                price_test = await self.test_real_time_pricing(symbol)
                if price_test['success']:
                    prices.append(price_test['results'])
                    timestamps.append(datetime.now().isoformat())
                
                if i < iterations - 1:
                    await asyncio.sleep(2)  # Wait 2 seconds between requests
            
            if prices:
                # Analyze consistency
                bid_prices = [p['bid'] for p in prices]
                ask_prices = [p['ask'] for p in prices]
                spreads = [p['spread'] for p in prices]
                
                results['results'] = {
                    'successful_requests': len(prices),
                    'bid_range': {'min': min(bid_prices), 'max': max(bid_prices)},
                    'ask_range': {'min': min(ask_prices), 'max': max(ask_prices)},
                    'spread_range': {'min': min(spreads), 'max': max(spreads)},
                    'price_samples': prices,
                    'timestamps': timestamps
                }
                results['success'] = True
                logger.info(f"Data consistency test completed: {len(prices)}/{iterations} successful requests")
            else:
                results['error'] = "No successful price requests"
                
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Data consistency test failed: {e}")
        
        return results
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all tests and compile results"""
        logger.info("Starting comprehensive MT5 data retrieval tests...")
        
        all_results = {
            'test_suite': 'MT5_Data_Retrieval_Comprehensive',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Test 1: Instrument availability
        all_results['tests']['instrument_availability'] = await self.test_instrument_availability()
        
        # Test 2: Real-time pricing for multiple symbols
        for symbol in ['XAU_USD', 'EUR_USD', 'GBP_USD']:
            test_key = f'real_time_pricing_{symbol}'
            all_results['tests'][test_key] = await self.test_real_time_pricing(symbol)
        
        # Test 3: Historical data for different timeframes
        for granularity in ['M1', 'M5', 'H1', 'D']:
            test_key = f'historical_data_XAU_USD_{granularity}'
            all_results['tests'][test_key] = await self.test_historical_data('XAU_USD', granularity, 50)
        
        # Test 4: Data consistency
        all_results['tests']['data_consistency'] = await self.test_data_consistency('XAU_USD', 3)
        
        # Compile summary
        total_tests = len(all_results['tests'])
        successful_tests = sum(1 for test in all_results['tests'].values() if test.get('success', False))
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'end_time': datetime.now().isoformat()
        }
        
        logger.info(f"Test suite completed: {successful_tests}/{total_tests} tests passed")
        return all_results

async def main():
    """Main test execution function"""
    test_suite = MT5DataRetrievalTest()
    
    # Note: Replace with actual OANDA demo credentials for testing
    if not test_suite.setup_oanda_connection():
        logger.error("Failed to setup OANDA connection. Please provide valid demo credentials.")
        return
    
    # Run comprehensive tests
    results = await test_suite.run_comprehensive_tests()
    
    # Save results to file
    with open('mt5_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("Test results saved to mt5_test_results.json")
    
    # Print summary
    print("\n" + "="*50)
    print("MT5 DATA RETRIEVAL TEST SUMMARY")
    print("="*50)
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Successful: {results['summary']['successful_tests']}")
    print(f"Success Rate: {results['summary']['success_rate']}")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
