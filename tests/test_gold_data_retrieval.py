#!/usr/bin/env python3

import asyncio
import aiohttp
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoldDataRetrievalTest:
    """
    Comprehensive test for XAU/USD (Gold) data retrieval from multiple sources.
    This simulates MT5/Exness gold trading data access.
    """
    
    def __init__(self):
        self.session = None
        
    async def setup_session(self):
        """Setup HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'ChartFix-MT5-Gold-Test/1.0'}
            )
    
    async def cleanup_session(self):
        """Cleanup HTTP session"""
        if self.session:
            await self.session.close()
    
    async def test_yahoo_gold_spot(self) -> Dict[str, Any]:
        """Test Yahoo Finance for gold spot price (GC=F)"""
        logger.info("Testing Yahoo Finance for gold spot price...")
        results = {
            'test_name': 'yahoo_gold_spot',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Gold futures symbol on Yahoo Finance
            url = "https://query1.finance.yahoo.com/v8/finance/chart/GC=F"
            params = {
                'interval': '1m',
                'range': '1d'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        meta = result.get('meta', {})
                        
                        # Get latest price data
                        timestamps = result.get('timestamp', [])
                        indicators = result.get('indicators', {})
                        quote = indicators.get('quote', [{}])[0] if indicators.get('quote') else {}
                        
                        latest_price = meta.get('regularMarketPrice')
                        
                        results['data'] = {
                            'symbol': 'XAU/USD (Gold)',
                            'source': 'Yahoo Finance GC=F',
                            'current_price': latest_price,
                            'currency': 'USD',
                            'previous_close': meta.get('previousClose'),
                            'day_high': meta.get('regularMarketDayHigh'),
                            'day_low': meta.get('regularMarketDayLow'),
                            'volume': meta.get('regularMarketVolume'),
                            'market_state': meta.get('marketState'),
                            'timezone': meta.get('timezone'),
                            'data_points': len(timestamps) if timestamps else 0
                        }
                        results['success'] = True
                        logger.info(f"Gold spot price: ${latest_price}")
                    else:
                        results['error'] = "No chart data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Yahoo gold spot test failed: {e}")
        
        return results
    
    async def test_yahoo_xau_usd(self) -> Dict[str, Any]:
        """Test Yahoo Finance for XAU/USD forex pair"""
        logger.info("Testing Yahoo Finance for XAU/USD forex pair...")
        results = {
            'test_name': 'yahoo_xau_usd',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # XAU/USD forex pair symbol
            url = "https://query1.finance.yahoo.com/v8/finance/chart/XAUUSD=X"
            params = {
                'interval': '1m',
                'range': '1d'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        meta = result.get('meta', {})
                        
                        results['data'] = {
                            'symbol': 'XAU/USD',
                            'source': 'Yahoo Finance XAUUSD=X',
                            'current_price': meta.get('regularMarketPrice'),
                            'currency': meta.get('currency'),
                            'exchange_name': meta.get('exchangeName'),
                            'previous_close': meta.get('previousClose'),
                            'day_high': meta.get('regularMarketDayHigh'),
                            'day_low': meta.get('regularMarketDayLow'),
                            'timezone': meta.get('timezone'),
                            'market_state': meta.get('marketState')
                        }
                        results['success'] = True
                        logger.info(f"XAU/USD price: ${meta.get('regularMarketPrice')}")
                    else:
                        results['error'] = "No chart data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Yahoo XAU/USD test failed: {e}")
        
        return results
    
    async def test_goldapi_io(self) -> Dict[str, Any]:
        """Test GoldAPI.io for real-time gold prices"""
        logger.info("Testing GoldAPI.io for real-time gold prices...")
        results = {
            'test_name': 'goldapi_io',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # GoldAPI.io free endpoint
            url = "https://www.goldapi.io/api/XAU/USD"
            headers = {
                'x-access-token': 'goldapi-demo-key',  # Demo key for testing
                'Content-Type': 'application/json'
            }
            
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results['data'] = {
                        'symbol': 'XAU/USD',
                        'source': 'GoldAPI.io',
                        'price': data.get('price'),
                        'currency': data.get('currency'),
                        'timestamp': data.get('timestamp'),
                        'price_gram_24k': data.get('price_gram_24k'),
                        'change': data.get('ch'),
                        'change_percent': data.get('chp')
                    }
                    results['success'] = True
                    logger.info(f"GoldAPI.io price: ${data.get('price')}")
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"GoldAPI.io test failed: {e}")
        
        return results
    
    async def test_historical_gold_data(self) -> Dict[str, Any]:
        """Test historical gold data retrieval"""
        logger.info("Testing historical gold data retrieval...")
        results = {
            'test_name': 'historical_gold_data',
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            await self.setup_session()
            
            # Get historical data from Yahoo Finance
            url = "https://query1.finance.yahoo.com/v8/finance/chart/GC=F"
            params = {
                'interval': '1h',
                'range': '5d'  # 5 days of hourly data
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    chart = data.get('chart', {})
                    if chart.get('result'):
                        result = chart['result'][0]
                        timestamps = result.get('timestamp', [])
                        indicators = result.get('indicators', {})
                        quote = indicators.get('quote', [{}])[0] if indicators.get('quote') else {}
                        
                        # Process OHLCV data
                        ohlcv_data = []
                        opens = quote.get('open', [])
                        highs = quote.get('high', [])
                        lows = quote.get('low', [])
                        closes = quote.get('close', [])
                        volumes = quote.get('volume', [])
                        
                        for i in range(min(len(timestamps), len(closes), 10)):  # Last 10 data points
                            if closes[i] is not None:
                                ohlcv_data.append({
                                    'timestamp': datetime.fromtimestamp(timestamps[i]).isoformat(),
                                    'open': opens[i] if i < len(opens) and opens[i] else None,
                                    'high': highs[i] if i < len(highs) and highs[i] else None,
                                    'low': lows[i] if i < len(lows) and lows[i] else None,
                                    'close': closes[i],
                                    'volume': volumes[i] if i < len(volumes) and volumes[i] else None
                                })
                        
                        results['data'] = {
                            'symbol': 'XAU/USD (Historical)',
                            'source': 'Yahoo Finance GC=F',
                            'interval': '1h',
                            'range': '5d',
                            'total_data_points': len(timestamps),
                            'sample_data': ohlcv_data,
                            'data_range': {
                                'from': datetime.fromtimestamp(timestamps[0]).isoformat() if timestamps else None,
                                'to': datetime.fromtimestamp(timestamps[-1]).isoformat() if timestamps else None
                            }
                        }
                        results['success'] = True
                        logger.info(f"Retrieved {len(timestamps)} historical data points")
                    else:
                        results['error'] = "No historical data found"
                else:
                    results['error'] = f"HTTP {response.status}"
                    
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Historical gold data test failed: {e}")
        
        return results
    
    async def test_price_consistency(self, iterations: int = 3) -> Dict[str, Any]:
        """Test price consistency across multiple sources"""
        logger.info(f"Testing gold price consistency across sources...")
        results = {
            'test_name': 'price_consistency',
            'iterations': iterations,
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'data': {}
        }
        
        try:
            consistency_data = []
            
            for i in range(iterations):
                logger.info(f"Consistency test iteration {i+1}/{iterations}")
                
                # Test multiple sources
                yahoo_spot = await self.test_yahoo_gold_spot()
                yahoo_xau = await self.test_yahoo_xau_usd()
                
                iteration_data = {
                    'iteration': i + 1,
                    'timestamp': datetime.now().isoformat(),
                    'yahoo_spot_success': yahoo_spot.get('success', False),
                    'yahoo_xau_success': yahoo_xau.get('success', False),
                    'yahoo_spot_price': yahoo_spot.get('data', {}).get('current_price'),
                    'yahoo_xau_price': yahoo_xau.get('data', {}).get('current_price')
                }
                
                # Calculate price difference if both sources available
                if iteration_data['yahoo_spot_price'] and iteration_data['yahoo_xau_price']:
                    price_diff = abs(iteration_data['yahoo_spot_price'] - iteration_data['yahoo_xau_price'])
                    price_diff_percent = (price_diff / iteration_data['yahoo_spot_price']) * 100
                    iteration_data['price_difference'] = price_diff
                    iteration_data['price_difference_percent'] = price_diff_percent
                
                consistency_data.append(iteration_data)
                
                if i < iterations - 1:
                    await asyncio.sleep(3)  # Wait between iterations
            
            # Calculate consistency metrics
            successful_iterations = sum(1 for d in consistency_data if d['yahoo_spot_success'] or d['yahoo_xau_success'])
            price_differences = [d.get('price_difference_percent', 0) for d in consistency_data if 'price_difference_percent' in d]
            
            results['data'] = {
                'iterations_data': consistency_data,
                'successful_iterations': successful_iterations,
                'success_rate': f"{(successful_iterations/iterations)*100:.1f}%",
                'average_price_difference': f"{sum(price_differences)/len(price_differences):.3f}%" if price_differences else "N/A",
                'max_price_difference': f"{max(price_differences):.3f}%" if price_differences else "N/A"
            }
            results['success'] = True
            logger.info(f"Price consistency test completed: {results['data']['success_rate']} success rate")
            
        except Exception as e:
            results['error'] = str(e)
            logger.error(f"Price consistency test failed: {e}")
        
        return results
    
    async def run_comprehensive_gold_tests(self) -> Dict[str, Any]:
        """Run all gold data retrieval tests"""
        logger.info("Starting comprehensive gold data retrieval tests...")
        
        all_results = {
            'test_suite': 'Gold_Data_Retrieval_Comprehensive',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Run all tests
        all_results['tests']['yahoo_gold_spot'] = await self.test_yahoo_gold_spot()
        all_results['tests']['yahoo_xau_usd'] = await self.test_yahoo_xau_usd()
        all_results['tests']['goldapi_io'] = await self.test_goldapi_io()
        all_results['tests']['historical_gold_data'] = await self.test_historical_gold_data()
        all_results['tests']['price_consistency'] = await self.test_price_consistency(2)
        
        # Compile summary
        total_tests = len(all_results['tests'])
        successful_tests = sum(1 for test in all_results['tests'].values() if test.get('success', False))
        
        all_results['summary'] = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            'end_time': datetime.now().isoformat()
        }
        
        await self.cleanup_session()
        logger.info(f"Gold test suite completed: {successful_tests}/{total_tests} tests passed")
        return all_results

async def main():
    """Main test execution function"""
    test_suite = GoldDataRetrievalTest()
    
    # Run comprehensive tests
    results = await test_suite.run_comprehensive_gold_tests()
    
    # Save results to file
    with open('gold_data_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("Test results saved to gold_data_test_results.json")
    
    # Print summary
    print("\n" + "="*60)
    print("GOLD DATA RETRIEVAL TEST SUMMARY")
    print("="*60)
    print(f"Total Tests: {results['summary']['total_tests']}")
    print(f"Successful: {results['summary']['successful_tests']}")
    print(f"Success Rate: {results['summary']['success_rate']}")
    print("="*60)
    
    # Print individual test results
    for test_name, test_result in results['tests'].items():
        status = "✅ PASS" if test_result.get('success') else "❌ FAIL"
        print(f"{status} {test_name}")
        if test_result.get('success') and 'data' in test_result:
            data = test_result['data']
            if 'current_price' in data:
                print(f"    Price: ${data['current_price']}")
            elif 'price' in data:
                print(f"    Price: ${data['price']}")
        if not test_result.get('success') and 'error' in test_result:
            print(f"    Error: {test_result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
