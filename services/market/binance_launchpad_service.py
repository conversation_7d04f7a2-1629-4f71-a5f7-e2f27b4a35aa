import asyncio
import logging
import time
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timezone

from services.core.error_service import handle_service_errors, retry_with_backoff
from services.data.cache_service import get_cache_service
from services.core.http_client_service import get_http_client
from utils.config import load_config

logger = logging.getLogger(__name__)

class BinanceLaunchpadService:
    """Service for monitoring Binance Launchpad announcements"""
    
    def __init__(self):
        self.cache_service = get_cache_service()
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Alert callbacks
        self.alert_callbacks = []
        
        # Processed announcements tracking
        self.processed_announcements = set()
        
        # Configuration
        self.enabled = True
        self.monitoring_interval = 1800  # 30 minutes
        self.binance_cms_url = 'https://www.binance.com/bapi/composite/v1/public/cms/article/list/query'
        self.catalog_ids = [48, 49]  # New Cryptocurrency Listing, Latest Binance News
        self.keywords = ['launchpad', 'airdrop', 'hodler', 'earn', 'staking', 'megadrop']
        
        logger.info("Binance Launchpad Service initialized")
    
    def register_alert_callback(self, callback: Callable):
        """Register callback for launchpad alerts"""
        self.alert_callbacks.append(callback)
        logger.info("Launchpad alert callback registered")
    
    async def start_monitoring(self):
        """Start launchpad monitoring"""
        if self.is_running or not self.enabled:
            return
            
        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("🚀 Binance Launchpad monitoring started")
    
    async def stop_monitoring(self):
        """Stop launchpad monitoring"""
        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 Binance Launchpad monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                await self._check_announcements()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in launchpad monitor loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    @handle_service_errors
    async def _check_announcements(self):
        """Check for new launchpad announcements"""
        try:
            new_announcements = []
            
            for catalog_id in self.catalog_ids:
                announcements = await self._fetch_announcements(catalog_id)
                if announcements:
                    filtered = self._filter_announcements(announcements)
                    new_announcements.extend(filtered)
            
            if new_announcements:
                await self._send_alerts(new_announcements)
                
        except Exception as e:
            logger.error(f"Error checking announcements: {e}")
    
    @retry_with_backoff(max_retries=3)
    async def _fetch_announcements(self, catalog_id: int) -> List[Dict[str, Any]]:
        """Fetch announcements from Binance CMS API"""
        cache_key = f"binance_cms_{catalog_id}"
        
        # Check cache first
        cached_data = self.cache_service.get(cache_key)
        if cached_data:
            return cached_data
        
        try:
            http_client = await get_http_client()
            params = {
                'type': 1,
                'catalogId': catalog_id,
                'pageNo': 1,
                'pageSize': 20
            }

            response = await http_client.get(self.binance_cms_url, params=params)

            if response.status == 200:
                data = await response.json()

                if data.get('success') and data.get('data', {}).get('catalogs'):
                    catalog = data['data']['catalogs'][0]
                    articles = catalog.get('articles', [])

                    # Cache for 15 minutes
                    self.cache_service.set(cache_key, articles, ttl=900)

                    logger.info(f"Fetched {len(articles)} articles from catalog {catalog_id}")
                    return articles
                else:
                    logger.warning(f"Invalid response from catalog {catalog_id}")
                    return []
            else:
                logger.error(f"HTTP {response.status} from catalog {catalog_id}")
                return []
                    
        except Exception as e:
            logger.error(f"Error fetching announcements for catalog {catalog_id}: {e}")
            return []
    
    def _filter_announcements(self, announcements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter announcements for launchpad/airdrop content"""
        filtered = []
        
        for article in announcements:
            article_id = article.get('id')
            title = article.get('title', '').lower()
            
            # Skip if already processed
            if article_id in self.processed_announcements:
                continue
            
            # Check for relevant keywords
            if any(keyword in title for keyword in self.keywords):
                # Extract project info
                project_name, token_symbol = self._extract_project_info(article.get('title', ''))
                
                # Add processed info
                article['project_name'] = project_name
                article['token_symbol'] = token_symbol
                article['announcement_type'] = self._get_announcement_type(title)
                
                filtered.append(article)
                self.processed_announcements.add(article_id)
                
                logger.info(f"New launchpad announcement: {project_name} ({token_symbol})")
        
        return filtered
    
    def _extract_project_info(self, title: str) -> tuple[str, str]:
        """Extract project name and token symbol from title"""
        import re
        
        # Find token symbol in parentheses
        symbol_match = re.search(r'\(([A-Z]{2,10})\)', title)
        token_symbol = symbol_match.group(1) if symbol_match else 'UNKNOWN'
        
        # Extract project name
        if symbol_match:
            project_name = title[:symbol_match.start()].strip()
            # Remove common prefixes
            prefixes = ['Introducing', 'Binance Will Add', 'Binance Adds']
            for prefix in prefixes:
                if project_name.startswith(prefix):
                    project_name = project_name[len(prefix):].strip()
        else:
            project_name = title
        
        # Clean up
        project_name = re.sub(r'\s+', ' ', project_name).strip()
        
        return project_name, token_symbol
    
    def _get_announcement_type(self, title: str) -> str:
        """Get announcement type"""
        if 'launchpad' in title:
            return 'launchpad'
        elif 'airdrop' in title or 'hodler' in title:
            return 'airdrop'
        elif 'earn' in title:
            return 'earn'
        elif 'staking' in title:
            return 'staking'
        elif 'megadrop' in title:
            return 'megadrop'
        else:
            return 'announcement'
    
    async def _send_alerts(self, announcements: List[Dict[str, Any]]):
        """Send alerts to registered callbacks"""
        try:
            for callback in self.alert_callbacks:
                await callback('binance_launchpad', {'announcements': announcements})
        except Exception as e:
            logger.error(f"Error sending launchpad alerts: {e}")

# Singleton instance
_binance_launchpad_service = None
_service_lock = threading.RLock()

def get_binance_launchpad_service() -> BinanceLaunchpadService:
    """Get singleton instance of BinanceLaunchpadService"""
    global _binance_launchpad_service
    with _service_lock:
        if _binance_launchpad_service is None:
            _binance_launchpad_service = BinanceLaunchpadService()
        return _binance_launchpad_service
